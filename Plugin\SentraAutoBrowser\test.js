const SentraAutoBrowser = require('./SentraAutoBrowser');

async function testPlugin() {
    console.log('🧪 开始测试 SentraAutoBrowser 插件...');

    // 测试配置
    const config = {
        BROWSER_HEADLESS: false,
        BROWSER_VIEWPORT_WIDTH: 1280,
        BROWSER_VIEWPORT_HEIGHT: 720,
        MAX_STEPS: 3,
        ENABLE_VISION: false // 测试时关闭视觉功能以避免API调用
    };

    const browser = new SentraAutoBrowser(config);

    try {
        // 测试1: 初始化浏览器
        console.log('📱 测试浏览器初始化...');
        await browser.initBrowser();
        console.log('✅ 浏览器初始化成功');

        // 测试2: 导航到页面
        console.log('🌐 测试页面导航...');
        await browser.page.goto('https://www.baidu.com', { waitUntil: 'networkidle2' });
        console.log('✅ 页面导航成功');

        // 测试3: 获取页面信息
        console.log('📊 测试页面信息获取...');
        const pageInfo = await browser.getPageInfo();
        console.log(`✅ 页面信息获取成功: ${pageInfo.title}`);
        console.log(`   可交互元素数量: ${pageInfo.interactiveElements.length}`);

        // 测试4: 截图功能
        console.log('📸 测试截图功能...');
        const screenshot = await browser.takeScreenshot();
        console.log(`✅ 截图成功，Base64长度: ${screenshot.length}`);

        // 测试5: 基本操作（如果有搜索框）
        const searchInput = pageInfo.interactiveElements.find(el => 
            el.tag === 'input' && (el.placeholder?.includes('搜索') || el.id?.includes('search'))
        );

        if (searchInput) {
            console.log('🔍 测试输入操作...');
            await browser.typeText(searchInput.sentraId, 'VCP协议测试');
            console.log('✅ 输入操作成功');
        }

        console.log('🎉 所有测试通过！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    } finally {
        // 清理资源
        await browser.closeBrowser();
        console.log('🧹 资源清理完成');
    }
}

// 测试VCP协议输入处理
async function testVCPInput() {
    console.log('\n🔧 测试VCP协议输入处理...');

    const testInput = JSON.stringify({
        command: 'screenshot',
        url: 'https://www.baidu.com',
        full_page: 'false',
        config: {
            BROWSER_HEADLESS: true,
            ENABLE_VISION: false
        }
    });

    // 模拟stdin输入
    const originalStdin = process.stdin;
    const mockStdin = require('stream').Readable.from([testInput]);
    
    // 临时替换stdin
    Object.defineProperty(process, 'stdin', {
        value: mockStdin,
        writable: true
    });

    try {
        // 这里应该调用main函数，但为了测试简化，我们直接测试核心逻辑
        const params = JSON.parse(testInput);
        const browser = new SentraAutoBrowser(params.config || {});
        
        await browser.initBrowser();
        await browser.page.goto(params.url, { waitUntil: 'networkidle2' });
        const screenshot = await browser.takeScreenshot(params.full_page === 'true');
        await browser.closeBrowser();

        console.log('✅ VCP协议输入处理测试成功');
        console.log(`   截图Base64长度: ${screenshot.length}`);

    } catch (error) {
        console.error('❌ VCP协议测试失败:', error.message);
    } finally {
        // 恢复原始stdin
        Object.defineProperty(process, 'stdin', {
            value: originalStdin,
            writable: true
        });
    }
}

// 运行测试
async function runAllTests() {
    await testPlugin();
    await testVCPInput();
    console.log('\n🏁 所有测试完成！');
}

if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { testPlugin, testVCPInput };
