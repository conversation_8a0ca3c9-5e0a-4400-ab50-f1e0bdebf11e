{"name": "SentraAutoBrowser", "displayName": "Sentra 智能浏览器自动化", "version": "1.0.0", "description": "基于AI驱动的智能浏览器自动化插件，支持自然语言描述的复杂网页操作任务。集成了智能元素检测、页面理解和自动化执行能力。", "pluginType": "synchronous", "entryPoint": {"command": "node SentraAutoBrowser.js"}, "communication": {"protocol": "stdio", "timeout": 60000}, "configSchema": {"OPENAI_API_KEY": {"type": "string", "description": "OpenAI API密钥，用于AI决策", "required": false}, "OPENAI_BASE_URL": {"type": "string", "description": "OpenAI API基础URL", "default": "https://api.openai.com/v1", "required": false}, "OPENAI_MODEL": {"type": "string", "description": "使用的OpenAI模型", "default": "gpt-4o-mini", "required": false}, "BROWSER_HEADLESS": {"type": "boolean", "description": "是否使用无头模式", "default": false, "required": false}, "BROWSER_VIEWPORT_WIDTH": {"type": "number", "description": "浏览器视窗宽度", "default": 1280, "required": false}, "BROWSER_VIEWPORT_HEIGHT": {"type": "number", "description": "浏览器视窗高度", "default": 720, "required": false}, "MAX_STEPS": {"type": "number", "description": "最大执行步数", "default": 10, "required": false}, "ENABLE_VISION": {"type": "boolean", "description": "是否启用视觉功能", "default": true, "required": false}}, "capabilities": {"invocationCommands": [{"command": "automate", "description": "执行基于自然语言描述的浏览器自动化任务。AI会智能分析页面内容，自动识别可交互元素，并执行相应操作。\n\n**参数说明:**\n- `command`: 固定为 `automate`\n- `task`: 自然语言描述的任务，例如：'打开百度，搜索人工智能，点击第一个结果'\n- `url`: (可选) 起始URL，如果不提供则使用当前页面\n- `max_steps`: (可选) 最大执行步数，默认为10\n- `headless`: (可选) 是否使用无头模式，默认为false\n- `enable_vision`: (可选) 是否启用视觉功能，默认为true\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」SentraAutoBrowser「末」,\ncommand: 「始」automate「末」,\ntask: 「始」打开百度，搜索'VCP协议'，点击第一个搜索结果「末」,\nurl: 「始」https://www.baidu.com「末」,\nmax_steps: 「始」5「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "执行复杂的网页自动化任务，如搜索、填表、购物等"}, {"command": "screenshot", "description": "对当前页面或指定URL进行截图，并返回Base64编码的图片数据。\n\n**参数说明:**\n- `command`: 固定为 `screenshot`\n- `url`: (可选) 要截图的URL，如果不提供则对当前页面截图\n- `full_page`: (可选) 是否截取整个页面，默认为false\n- `element_selector`: (可选) 只截取特定元素的CSS选择器\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」SentraAutoBrowser「末」,\ncommand: 「始」screenshot「末」,\nurl: 「始」https://www.google.com「末」,\nfull_page: 「始」true「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "截取网页截图用于分析或记录"}, {"command": "extract_data", "description": "从网页中智能提取结构化数据。AI会分析页面内容并提取指定类型的信息。\n\n**参数说明:**\n- `command`: 固定为 `extract_data`\n- `url`: 要提取数据的URL\n- `data_type`: 要提取的数据类型，如：'新闻标题', '商品信息', '联系方式', '表格数据'等\n- `format`: (可选) 返回格式，支持 'json', 'markdown', 'text'，默认为'json'\n- `max_items`: (可选) 最大提取条目数，默认为10\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」SentraAutoBrowser「末」,\ncommand: 「始」extract_data「末」,\nurl: 「始」https://news.sina.com.cn「末」,\ndata_type: 「始」新闻标题和链接「末」,\nformat: 「始」json「末」,\nmax_items: 「始」20「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "智能提取网页中的结构化数据"}, {"command": "wait_and_interact", "description": "等待页面元素出现并进行交互。适用于动态加载的页面内容。\n\n**参数说明:**\n- `command`: 固定为 `wait_and_interact`\n- `wait_for`: 等待的元素描述或选择器\n- `action`: 要执行的操作：'click', 'type', 'hover', 'scroll'\n- `text`: (当action为type时) 要输入的文本\n- `timeout`: (可选) 等待超时时间(毫秒)，默认为30000\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」SentraAutoBrowser「末」,\ncommand: 「始」wait_and_interact「末」,\nwait_for: 「始」搜索按钮「末」,\naction: 「始」click「末」,\ntimeout: 「始」10000「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "等待动态元素加载完成后进行交互"}]}, "webSocketPush": {"enabled": true, "usePluginResultAsMessage": false, "messageType": "SentraAutoBrowser", "targetClientType": "VCPLog"}}