const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

class SentraAutoBrowser {
    constructor(config = {}) {
        this.config = {
            OPENAI_API_KEY: config.OPENAI_API_KEY || process.env.OPENAI_API_KEY,
            OPENAI_BASE_URL: config.OPENAI_BASE_URL || process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
            OPENAI_MODEL: config.OPENAI_MODEL || process.env.OPENAI_MODEL || 'gpt-4o-mini',
            BROWSER_HEADLESS: config.BROWSER_HEADLESS !== undefined ? config.BROWSER_HEADLESS : false,
            BROWSER_VIEWPORT_WIDTH: config.BROWSER_VIEWPORT_WIDTH || 1280,
            BROWSER_VIEWPORT_HEIGHT: config.BROWSER_VIEWPORT_HEIGHT || 720,
            MAX_STEPS: config.MAX_STEPS || 10,
            ENABLE_VISION: config.ENABLE_VISION !== undefined ? config.ENABLE_VISION : true
        };
        this.browser = null;
        this.page = null;
    }

    async initBrowser() {
        if (!this.browser) {
            this.browser = await puppeteer.launch({
                headless: this.config.BROWSER_HEADLESS,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled'
                ]
            });
            this.page = await this.browser.newPage();
            await this.page.setViewport({
                width: this.config.BROWSER_VIEWPORT_WIDTH,
                height: this.config.BROWSER_VIEWPORT_HEIGHT
            });
            
            // 设置用户代理
            await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        }
    }

    async closeBrowser() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
            this.page = null;
        }
    }

    async takeScreenshot(fullPage = false, elementSelector = null) {
        if (!this.page) {
            throw new Error('浏览器未初始化');
        }

        let screenshotOptions = {
            type: 'png',
            encoding: 'base64',
            fullPage: fullPage
        };

        if (elementSelector) {
            const element = await this.page.$(elementSelector);
            if (element) {
                return await element.screenshot({ encoding: 'base64' });
            }
        }

        return await this.page.screenshot(screenshotOptions);
    }

    async getPageInfo() {
        if (!this.page) {
            throw new Error('浏览器未初始化');
        }

        const pageInfo = await this.page.evaluate(() => {
            // 获取页面基本信息
            const title = document.title;
            const url = window.location.href;
            
            // 获取可交互元素
            const interactiveElements = [];
            const selectors = [
                'button', 'input', 'select', 'textarea', 'a[href]',
                '[onclick]', '[role="button"]', '[tabindex]'
            ];
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach((el, index) => {
                    if (el.offsetParent !== null) { // 只获取可见元素
                        const rect = el.getBoundingClientRect();
                        const elementInfo = {
                            tag: el.tagName.toLowerCase(),
                            type: el.type || '',
                            text: el.textContent?.trim().substring(0, 100) || '',
                            placeholder: el.placeholder || '',
                            id: el.id || '',
                            className: el.className || '',
                            href: el.href || '',
                            selector: `${selector}:nth-of-type(${index + 1})`,
                            position: {
                                x: Math.round(rect.left),
                                y: Math.round(rect.top),
                                width: Math.round(rect.width),
                                height: Math.round(rect.height)
                            }
                        };
                        
                        // 为元素添加唯一标识
                        if (!el.getAttribute('data-sentra-id')) {
                            el.setAttribute('data-sentra-id', `sentra-${Date.now()}-${index}`);
                            elementInfo.sentraId = el.getAttribute('data-sentra-id');
                        }
                        
                        interactiveElements.push(elementInfo);
                    }
                });
            });
            
            return {
                title,
                url,
                interactiveElements: interactiveElements.slice(0, 50) // 限制元素数量
            };
        });

        return pageInfo;
    }

    async callAI(prompt, includeScreenshot = true) {
        if (!this.config.OPENAI_API_KEY) {
            throw new Error('未配置OpenAI API密钥');
        }

        const messages = [
            {
                role: 'system',
                content: `你是一个智能浏览器自动化助手。你需要分析网页内容并决定下一步操作。

可用的操作类型：
1. click - 点击元素 (需要提供元素的sentraId或描述)
2. type - 在输入框中输入文本 (需要提供元素的sentraId和文本内容)
3. scroll - 滚动页面 (可选参数：direction: up/down, distance: 像素)
4. wait - 等待指定时间 (毫秒)
5. navigate - 导航到新URL
6. complete - 任务完成

请根据当前页面状态和用户任务，返回JSON格式的操作指令：
{
    "action": "操作类型",
    "target": "目标元素的sentraId或描述",
    "value": "操作值(如输入的文本、URL等)",
    "reasoning": "操作理由"
}`
            },
            {
                role: 'user',
                content: prompt
            }
        ];

        if (includeScreenshot && this.config.ENABLE_VISION) {
            try {
                const screenshot = await this.takeScreenshot();
                messages[1].content = [
                    { type: 'text', text: prompt },
                    { 
                        type: 'image_url', 
                        image_url: { url: `data:image/png;base64,${screenshot}` }
                    }
                ];
            } catch (error) {
                console.error('截图失败:', error);
            }
        }

        const response = await fetch(`${this.config.OPENAI_BASE_URL}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.OPENAI_API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: this.config.OPENAI_MODEL,
                messages: messages,
                max_tokens: 1000,
                temperature: 0.1
            })
        });

        if (!response.ok) {
            throw new Error(`AI API调用失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        const content = data.choices[0].message.content;
        
        try {
            return JSON.parse(content);
        } catch (error) {
            // 如果不是JSON格式，尝试提取JSON部分
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            throw new Error('AI返回的不是有效的JSON格式');
        }
    }

    async executeAction(action) {
        const { action: actionType, target, value, reasoning } = action;
        
        console.log(`执行操作: ${actionType}, 目标: ${target}, 值: ${value}, 理由: ${reasoning}`);

        switch (actionType) {
            case 'click':
                await this.clickElement(target);
                break;
            case 'type':
                await this.typeText(target, value);
                break;
            case 'scroll':
                await this.scrollPage(value);
                break;
            case 'wait':
                await this.page.waitForTimeout(parseInt(value) || 1000);
                break;
            case 'navigate':
                await this.page.goto(value, { waitUntil: 'networkidle2' });
                break;
            case 'complete':
                return { completed: true, message: reasoning };
            default:
                throw new Error(`未知的操作类型: ${actionType}`);
        }

        return { completed: false, action: actionType, target, value, reasoning };
    }

    async clickElement(target) {
        // 首先尝试通过sentraId查找
        let element = await this.page.$(`[data-sentra-id="${target}"]`);
        
        if (!element) {
            // 如果没找到，尝试通过文本内容查找
            element = await this.page.evaluateHandle((targetText) => {
                const elements = Array.from(document.querySelectorAll('button, a, [onclick], [role="button"]'));
                return elements.find(el => 
                    el.textContent.trim().includes(targetText) ||
                    el.getAttribute('aria-label')?.includes(targetText) ||
                    el.title?.includes(targetText)
                );
            }, target);
        }

        if (element && element.asElement) {
            await element.asElement().click();
        } else {
            throw new Error(`找不到目标元素: ${target}`);
        }
    }

    async typeText(target, text) {
        let element = await this.page.$(`[data-sentra-id="${target}"]`);
        
        if (!element) {
            // 尝试通过placeholder或label查找输入框
            element = await this.page.evaluateHandle((targetText) => {
                const inputs = Array.from(document.querySelectorAll('input, textarea'));
                return inputs.find(input => 
                    input.placeholder?.includes(targetText) ||
                    input.getAttribute('aria-label')?.includes(targetText) ||
                    input.name?.includes(targetText)
                );
            }, target);
        }

        if (element && element.asElement) {
            await element.asElement().click();
            await element.asElement().clear();
            await element.asElement().type(text);
        } else {
            throw new Error(`找不到输入框: ${target}`);
        }
    }

    async scrollPage(direction = 'down', distance = 500) {
        const scrollDistance = direction === 'up' ? -distance : distance;
        await this.page.evaluate((dist) => {
            window.scrollBy(0, dist);
        }, scrollDistance);
    }

    async automateTask(task, url = null, maxSteps = null) {
        const steps = maxSteps || this.config.MAX_STEPS;
        const results = [];

        try {
            await this.initBrowser();

            if (url) {
                await this.page.goto(url, { waitUntil: 'networkidle2' });
            }

            for (let step = 0; step < steps; step++) {
                const pageInfo = await this.getPageInfo();
                const prompt = `
任务: ${task}

当前页面信息:
- 标题: ${pageInfo.title}
- URL: ${pageInfo.url}
- 可交互元素: ${JSON.stringify(pageInfo.interactiveElements.slice(0, 10), null, 2)}

当前步骤: ${step + 1}/${steps}
已执行的操作: ${JSON.stringify(results, null, 2)}

请分析当前页面状态，决定下一步操作来完成任务。如果任务已完成，请返回action为"complete"。
                `;

                const action = await this.callAI(prompt, this.config.ENABLE_VISION);
                const result = await this.executeAction(action);
                results.push(result);

                if (result.completed) {
                    break;
                }

                // 等待页面稳定
                await this.page.waitForTimeout(1000);
            }

            return {
                status: 'success',
                result: {
                    task: task,
                    steps_executed: results.length,
                    final_url: await this.page.url(),
                    final_title: await this.page.title(),
                    actions: results
                },
                messageForAI: `任务执行完成。共执行了${results.length}个步骤。最终页面: ${await this.page.title()}`
            };

        } catch (error) {
            return {
                status: 'error',
                error: error.message,
                result: {
                    task: task,
                    steps_executed: results.length,
                    actions: results
                },
                messageForAI: `任务执行失败: ${error.message}`
            };
        }
    }

    async extractData(url, dataType, format = 'json', maxItems = 10) {
        try {
            await this.initBrowser();
            await this.page.goto(url, { waitUntil: 'networkidle2' });

            const pageInfo = await this.getPageInfo();
            const prompt = `
请从以下网页中提取"${dataType}"类型的数据:

页面标题: ${pageInfo.title}
页面URL: ${url}

要求:
1. 提取${dataType}相关的信息
2. 最多提取${maxItems}条记录
3. 返回${format}格式的数据
4. 确保数据准确和完整

请分析页面内容并提取相关数据。
            `;

            const extractedData = await this.callAI(prompt, this.config.ENABLE_VISION);

            return {
                status: 'success',
                result: {
                    url: url,
                    data_type: dataType,
                    format: format,
                    extracted_data: extractedData,
                    total_items: Array.isArray(extractedData) ? extractedData.length : 1
                },
                messageForAI: `成功从${url}提取了${dataType}数据，共${Array.isArray(extractedData) ? extractedData.length : 1}条记录`
            };

        } catch (error) {
            return {
                status: 'error',
                error: error.message,
                messageForAI: `数据提取失败: ${error.message}`
            };
        }
    }

    async waitAndInteract(waitFor, action, text = '', timeout = 30000) {
        try {
            await this.initBrowser();

            // 等待元素出现
            await this.page.waitForFunction(
                (waitText) => {
                    const elements = Array.from(document.querySelectorAll('*'));
                    return elements.some(el =>
                        el.textContent?.includes(waitText) ||
                        el.placeholder?.includes(waitText) ||
                        el.getAttribute('aria-label')?.includes(waitText)
                    );
                },
                { timeout },
                waitFor
            );

            // 执行操作
            const actionObj = {
                action: action,
                target: waitFor,
                value: text,
                reasoning: `等待元素"${waitFor}"出现后执行${action}操作`
            };

            const result = await this.executeAction(actionObj);

            return {
                status: 'success',
                result: result,
                messageForAI: `成功等待元素"${waitFor}"出现并执行了${action}操作`
            };

        } catch (error) {
            return {
                status: 'error',
                error: error.message,
                messageForAI: `等待和交互失败: ${error.message}`
            };
        }
    }
}

// 主函数 - 处理VCP协议输入
async function main() {
    let input = '';

    // 读取stdin输入
    for await (const chunk of process.stdin) {
        input += chunk;
    }

    try {
        const params = JSON.parse(input);
        const browser = new SentraAutoBrowser(params.config || {});
        let result;

        switch (params.command) {
            case 'automate':
                result = await browser.automateTask(
                    params.task,
                    params.url,
                    params.max_steps ? parseInt(params.max_steps) : null
                );
                break;

            case 'screenshot':
                await browser.initBrowser();
                if (params.url) {
                    await browser.page.goto(params.url, { waitUntil: 'networkidle2' });
                }
                const screenshot = await browser.takeScreenshot(
                    params.full_page === 'true',
                    params.element_selector
                );
                result = {
                    status: 'success',
                    result: 'Screenshot captured successfully',
                    base64: screenshot,
                    messageForAI: '页面截图已生成'
                };
                break;

            case 'extract_data':
                result = await browser.extractData(
                    params.url,
                    params.data_type,
                    params.format || 'json',
                    params.max_items ? parseInt(params.max_items) : 10
                );
                break;

            case 'wait_and_interact':
                result = await browser.waitAndInteract(
                    params.wait_for,
                    params.action,
                    params.text || '',
                    params.timeout ? parseInt(params.timeout) : 30000
                );
                break;

            default:
                result = {
                    status: 'error',
                    error: `未知的命令: ${params.command}`,
                    messageForAI: `不支持的操作命令: ${params.command}`
                };
        }

        // 清理资源
        await browser.closeBrowser();

        // 输出结果
        console.log(JSON.stringify(result));

    } catch (error) {
        console.log(JSON.stringify({
            status: 'error',
            error: error.message,
            messageForAI: `插件执行失败: ${error.message}`
        }));
    }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
    main().catch(error => {
        console.log(JSON.stringify({
            status: 'error',
            error: error.message,
            messageForAI: `插件执行失败: ${error.message}`
        }));
    });
}

module.exports = SentraAutoBrowser;
