# SentraAutoBrowser - 智能浏览器自动化插件

## 📖 简介

SentraAutoBrowser 是一个基于AI驱动的智能浏览器自动化插件，专为VCPToolBox设计。它结合了Puppeteer的强大浏览器控制能力和AI的智能决策能力，让AI能够通过自然语言描述来执行复杂的网页自动化任务。

## ✨ 特性

- 🧠 **AI驱动决策**: 使用大语言模型智能分析页面内容并决定操作步骤
- 🎯 **智能元素检测**: 自动识别和标记页面中的可交互元素
- 📱 **多种操作模式**: 支持点击、输入、滚动、等待、截图等多种操作
- 🔄 **自适应执行**: 能够根据页面变化动态调整执行策略
- 📊 **数据提取**: 智能提取网页中的结构化数据
- 🖼️ **视觉理解**: 支持基于截图的页面理解和操作决策
- ⚡ **高效执行**: 优化的执行流程，减少不必要的等待时间

## 🚀 快速开始

### 安装依赖

```bash
cd Plugin/SentraAutoBrowser
npm install
```

### 配置

1. 复制配置文件：
```bash
cp config.env.example config.env
```

2. 编辑 `config.env` 文件，填写必要的配置：
```env
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini
BROWSER_HEADLESS=false
```

### 使用示例

#### 1. 自动化任务执行

```
<<<[TOOL_REQUEST]>>>
tool_name: 「始」SentraAutoBrowser「末」,
command: 「始」automate「末」,
task: 「始」打开百度，搜索'VCP协议'，点击第一个搜索结果「末」,
url: 「始」https://www.baidu.com「末」,
max_steps: 「始」5「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 2. 网页截图

```
<<<[TOOL_REQUEST]>>>
tool_name: 「始」SentraAutoBrowser「末」,
command: 「始」screenshot「末」,
url: 「始」https://www.google.com「末」,
full_page: 「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 3. 数据提取

```
<<<[TOOL_REQUEST]>>>
tool_name: 「始」SentraAutoBrowser「末」,
command: 「始」extract_data「末」,
url: 「始」https://news.sina.com.cn「末」,
data_type: 「始」新闻标题和链接「末」,
format: 「始」json「末」,
max_items: 「始」10「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 4. 等待并交互

```
<<<[TOOL_REQUEST]>>>
tool_name: 「始」SentraAutoBrowser「末」,
command: 「始」wait_and_interact「末」,
wait_for: 「始」搜索按钮「末」,
action: 「始」click「末」,
timeout: 「始」10000「末」
<<<[END_TOOL_REQUEST]>>>
```

## 🛠️ 配置选项

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `OPENAI_API_KEY` | string | - | OpenAI API密钥 |
| `OPENAI_BASE_URL` | string | https://api.openai.com/v1 | OpenAI API基础URL |
| `OPENAI_MODEL` | string | gpt-4o-mini | 使用的AI模型 |
| `BROWSER_HEADLESS` | boolean | false | 是否使用无头模式 |
| `BROWSER_VIEWPORT_WIDTH` | number | 1280 | 浏览器视窗宽度 |
| `BROWSER_VIEWPORT_HEIGHT` | number | 720 | 浏览器视窗高度 |
| `MAX_STEPS` | number | 10 | 最大执行步数 |
| `ENABLE_VISION` | boolean | true | 是否启用视觉功能 |

## 🎯 支持的命令

### automate
执行基于自然语言描述的浏览器自动化任务。

**参数:**
- `task`: 任务描述
- `url`: (可选) 起始URL
- `max_steps`: (可选) 最大执行步数
- `headless`: (可选) 是否使用无头模式

### screenshot
对页面进行截图。

**参数:**
- `url`: (可选) 要截图的URL
- `full_page`: (可选) 是否截取整个页面
- `element_selector`: (可选) 特定元素的CSS选择器

### extract_data
从网页中智能提取结构化数据。

**参数:**
- `url`: 目标URL
- `data_type`: 数据类型描述
- `format`: (可选) 返回格式
- `max_items`: (可选) 最大提取条目数

### wait_and_interact
等待页面元素出现并进行交互。

**参数:**
- `wait_for`: 等待的元素描述
- `action`: 要执行的操作
- `text`: (可选) 输入文本
- `timeout`: (可选) 等待超时时间

## 🔧 技术架构

- **浏览器引擎**: Puppeteer
- **AI决策**: OpenAI GPT系列模型
- **元素检测**: 智能DOM分析和标记
- **视觉理解**: 基于截图的页面分析
- **错误处理**: 完善的异常处理和恢复机制

## 📝 注意事项

1. **API密钥**: 确保正确配置OpenAI API密钥
2. **网络环境**: 需要稳定的网络连接访问目标网站和AI API
3. **浏览器资源**: 插件会启动浏览器进程，注意系统资源使用
4. **反爬虫**: 某些网站可能有反爬虫机制，请合理使用
5. **隐私安全**: 不要在不安全的环境中使用敏感网站

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 📄 许可证

本项目采用 CC BY-NC-SA 4.0 许可证。
